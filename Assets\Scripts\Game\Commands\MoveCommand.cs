using System;
using System.Collections;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to move a character to a target position over a duration.
    /// </summary>
    public class MoveCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the move command.</summary>
        public MoveParams Parameters { get; }

        /// <summary>Creates a new MoveCommand.</summary>
        /// <param name="parameters">Move parameters.</param>
        public MoveCommand(MoveParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            // TODO: Implement movement logic for Parameters.character to Parameters.to over Parameters.duration
            yield break;
        }
    }

    /// <summary>Parameters for Move command.</summary>
    [Serializable]
    public struct MoveParams { public string character; public Vector3 to; public float duration; }
}
